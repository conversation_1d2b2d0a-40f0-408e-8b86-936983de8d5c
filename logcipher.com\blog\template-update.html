<!-- 这是一个模板文件，展示如何更新其他blog文章页面 -->

<!-- 1. 在<body>标签后添加进度条和背景元素 -->
<body>
    <!-- Reading Progress Bar -->
    <div class="reading-progress">
        <div class="reading-progress-bar"></div>
    </div>

    <!-- Background Elements -->
    <div class="blog-background-elements">
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
    </div>

<!-- 2. 更新文章内容布局 -->
    <!-- Article Content -->
    <section class="article-content">
        <div class="article-layout">
            <main class="article-main">
                <div class="article-body">
                    <!-- 文章内容保持不变 -->
                </div>
            </main>

            <!-- Sidebar -->
            <aside class="article-sidebar">
                <!-- Table of Contents -->
                <div class="sidebar-section">
                    <h3 class="sidebar-title">Table of Contents</h3>
                    <ul class="toc-list">
                        <!-- TOC will be generated by JavaScript -->
                    </ul>
                </div>

                <!-- Recommended Articles -->
                <div class="sidebar-section sidebar-recommended">
                    <h3 class="sidebar-title">Recommended Reading</h3>
                    
                    <div class="sidebar-recommended-item">
                        <div class="sidebar-recommended-image">
                            <img src="https://image.pollinations.ai/prompt/相关图片prompt" alt="文章标题" loading="lazy">
                            <div class="sidebar-recommended-category">分类</div>
                        </div>
                        <div class="sidebar-recommended-content">
                            <h4 class="sidebar-recommended-title">推荐文章标题</h4>
                            <div class="sidebar-recommended-meta">阅读时间</div>
                            <a href="文章链接.html" class="sidebar-recommended-link">Read More</a>
                        </div>
                    </div>

                    <!-- 重复上面的结构添加更多推荐文章 -->
                </div>

                <!-- Author Info -->
                <div class="sidebar-section">
                    <h3 class="sidebar-title">About the Author</h3>
                    <div class="author-info">
                        <div class="author-avatar">✍️</div>
                        <div class="author-name">Logcipher Team</div>
                        <div class="author-bio">Passionate about empowering writers with AI-powered tools and sharing insights on creative writing.</div>
                    </div>
                </div>

                <!-- Share Buttons -->
                <div class="sidebar-section">
                    <h3 class="sidebar-title">Share This Article</h3>
                    <div class="share-buttons">
                        <a href="#" class="share-btn twitter" onclick="shareArticle('twitter')" title="Share on Twitter">🐦</a>
                        <a href="#" class="share-btn facebook" onclick="shareArticle('facebook')" title="Share on Facebook">📘</a>
                        <a href="#" class="share-btn linkedin" onclick="shareArticle('linkedin')" title="Share on LinkedIn">💼</a>
                    </div>
                </div>
            </aside>
        </div>
    </section>

<!-- 3. 移除旧的推荐模块section -->
<!-- 删除整个 <section class="recommended-section"> 部分 -->

<!-- 4. 更新footer为完整版本 -->
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-main">
                    <div class="footer-brand">
                        <div class="brand-info">
                            <h3 class="brand-name">Logcipher.<span class="brand-highlight">com</span></h3>
                            <p class="brand-description">Professional AI-powered writing tools designed for accuracy and
                                creativity. All content generation is performed locally in your browser for maximum
                                privacy and speed.</p>
                        </div>
                        <div class="social-icons">
                            <!-- 社交媒体图标 SVG 代码 -->
                        </div>
                    </div>
                    <div class="footer-tools">
                        <div class="links-section">
                            <h4>AI Tools</h4>
                            <ul>
                                <li><a href="../index.html#tools">Story Generator</a></li>
                                <li><a href="../index.html#tools">Dialogue Creator</a></li>
                                <li><a href="../index.html#tools">Poetry Maker</a></li>
                                <li><a href="../index.html#tools">Title Generator</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="footer-links">
                        <div class="links-section">
                            <h4>Quick Links</h4>
                            <ul>
                                <li><a href="../index.html">Home</a></li>
                                <li><a href="index.html">Blog</a></li>
                                <li><a href="../about.html">About Us</a></li>
                                <li><a href="../contact.html">Contact Us</a></li>
                                <li><a href="../privacy.html">Privacy Policy</a></li>
                                <li><a href="../terms.html">Terms of Service</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Logcipher.com. All rights reserved.</p>
            </div>
        </div>
    </footer>

<!-- 5. 添加JavaScript -->
    <script src="../js/main.js"></script>
    <script src="../js/blog.js"></script>
</body>

<!-- 
更新步骤总结：
1. 添加阅读进度条和背景元素
2. 更改文章布局为双栏（主内容+侧边栏）
3. 移除底部的推荐模块section
4. 更新footer为完整版本
5. 添加blog.js脚本
6. 确保推荐文章链接正确
-->
