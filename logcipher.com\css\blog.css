/* Blog Specific Styles */

/* Reading Progress Bar */
.reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(99, 102, 241, 0.1);
    z-index: 1000;
}

.reading-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.1s ease;
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
}

/* Enhanced Background Elements */
.blog-background-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.floating-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(99, 102, 241, 0.05), rgba(236, 72, 153, 0.05));
    animation: float 20s infinite ease-in-out;
}

.floating-shape:nth-child(1) {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 5%;
    animation-delay: 0s;
}

.floating-shape:nth-child(2) {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 7s;
}

.floating-shape:nth-child(3) {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 20%;
    animation-delay: 14s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

/* Blog Hero Section */
.blog-hero {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.blog-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.blog-hero-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.blog-hero-title {
    font-size: 3.5rem;
    color: white;
    margin-bottom: 1.5rem;
    font-weight: 800;
}

.blog-hero-description {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto 2rem;
    line-height: 1.6;
}

.blog-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.blog-stats .stat-item {
    text-align: center;
    color: white;
}

.blog-stats .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.blog-stats .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Featured Article */
.featured-article {
    padding: 80px 0;
    background: var(--bg-secondary);
}

.featured-card {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    transition: var(--transition);
}

.featured-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.featured-image {
    position: relative;
    overflow: hidden;
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.featured-card:hover .featured-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.featured-content {
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.featured-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.featured-meta .category {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 500;
}

.featured-meta .read-time,
.featured-meta .date {
    color: var(--text-light);
}

.featured-title {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.featured-excerpt {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.featured-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
}

.featured-link:hover {
    color: var(--primary-dark);
}

.featured-link .arrow {
    transition: var(--transition);
}

.featured-link:hover .arrow {
    transform: translateX(5px);
}

/* Blog Grid */
.blog-grid-section {
    padding: 80px 0;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.blog-card {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.blog-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

.blog-category {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.blog-content {
    padding: 1.5rem;
}

.blog-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: var(--text-light);
}

.blog-title {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.blog-excerpt {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.blog-link {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
}

.blog-link:hover {
    color: var(--primary-dark);
}

/* Newsletter Section */
.newsletter-section {
    padding: 60px 0;
    background: var(--bg-secondary);
}

.newsletter-card {
    background: white;
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 2rem;
    align-items: center;
}

.newsletter-content h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.newsletter-content p {
    color: var(--text-secondary);
    margin: 0;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
}

.newsletter-input {
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    min-width: 250px;
    transition: var(--transition);
}

.newsletter-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.newsletter-btn {
    padding: 0.75rem 1.5rem;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.newsletter-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Article Page Styles */
.article-header {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, var(--bg-secondary), rgba(99, 102, 241, 0.05));
    text-align: center;
    position: relative;
    overflow: hidden;
}

.article-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(99,102,241,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.5;
}

.article-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    font-size: 0.9rem;
    color: var(--text-light);
    position: relative;
    z-index: 2;
}

.article-meta span {
    background: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.article-title {
    font-size: 3rem;
    color: var(--text-primary);
    max-width: 800px;
    margin: 0 auto 1rem;
    line-height: 1.2;
    position: relative;
    z-index: 2;
}

.article-excerpt {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

.article-content {
    padding: 60px 0;
}

.article-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 4rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.article-main {
    min-width: 0;
}

.article-body {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-primary);
}

.article-body h2 {
    margin-top: 3rem;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    position: relative;
    padding-left: 1rem;
}

.article-body h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.article-body h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.article-body p {
    margin-bottom: 1.5rem;
}

.article-body img {
    width: 100%;
    border-radius: var(--border-radius);
    margin: 2rem 0;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.article-body img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

.article-body blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 2rem;
    margin: 2rem 0;
    font-style: italic;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: 1.5rem 2rem;
    border-radius: var(--border-radius);
    position: relative;
}

.article-body blockquote::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.3;
}

.article-body ul, .article-body ol {
    margin: 1.5rem 0;
    padding-left: 2rem;
}

.article-body li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* Article Sidebar */
.article-sidebar {
    position: sticky;
    top: 100px;
    height: fit-content;
}

.sidebar-section {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.sidebar-title {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-weight: 700;
}

/* Table of Contents */
.toc-list {
    list-style: none;
    padding: 0;
}

.toc-item {
    margin-bottom: 0.5rem;
}

.toc-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
    display: block;
    padding: 0.5rem 0;
    border-left: 2px solid transparent;
    padding-left: 1rem;
}

.toc-link:hover {
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.toc-link.active {
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    font-weight: 600;
}

/* Sidebar Recommended Articles */
.sidebar-recommended {
    margin-top: 0;
}

.sidebar-recommended-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-recommended-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.sidebar-recommended-image {
    width: 80px;
    height: 60px;
    border-radius: var(--border-radius);
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
}

.sidebar-recommended-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.sidebar-recommended-item:hover .sidebar-recommended-image img {
    transform: scale(1.1);
}

.sidebar-recommended-category {
    position: absolute;
    top: 2px;
    left: 2px;
    background: var(--primary-color);
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 6px;
    font-size: 0.6rem;
    font-weight: 600;
}

.sidebar-recommended-content {
    flex: 1;
    min-width: 0;
}

.sidebar-recommended-title {
    font-size: 0.9rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-weight: 600;
}

.sidebar-recommended-meta {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.sidebar-recommended-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 600;
    transition: var(--transition);
}

.sidebar-recommended-link:hover {
    color: var(--primary-dark);
}

/* Author Info */
.author-info {
    text-align: center;
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.author-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.author-bio {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* Share Buttons */
.share-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.share-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: white;
    transition: var(--transition);
    font-size: 0.9rem;
}

.share-btn.twitter {
    background: #1da1f2;
}

.share-btn.facebook {
    background: #4267b2;
}

.share-btn.linkedin {
    background: #0077b5;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .article-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .article-sidebar {
        position: static;
        order: -1;
    }

    .sidebar-section {
        margin-bottom: 1rem;
    }

    .floating-shape {
        display: none;
    }
}

@media (max-width: 768px) {
    .blog-hero-title {
        font-size: 2.5rem;
    }

    .blog-stats {
        gap: 2rem;
    }

    .featured-card {
        grid-template-columns: 1fr;
    }

    .featured-content {
        padding: 2rem;
    }

    .blog-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .newsletter-card {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .newsletter-input {
        min-width: auto;
    }

    .article-title {
        font-size: 2rem;
    }

    .article-meta {
        flex-direction: column;
        gap: 0.5rem;
        align-items: center;
    }

    .article-layout {
        padding: 0 15px;
        gap: 1.5rem;
    }

    .sidebar-section {
        padding: 1.5rem;
    }

    .sidebar-recommended-item {
        gap: 0.75rem;
    }

    .sidebar-recommended-image {
        width: 60px;
        height: 45px;
    }

    .share-buttons {
        gap: 0.75rem;
    }
}
