/* Blog Specific Styles */

/* Blog Hero Section */
.blog-hero {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
}

.blog-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.blog-hero-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.blog-hero-title {
    font-size: 3.5rem;
    color: white;
    margin-bottom: 1.5rem;
    font-weight: 800;
}

.blog-hero-description {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto 2rem;
    line-height: 1.6;
}

.blog-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.blog-stats .stat-item {
    text-align: center;
    color: white;
}

.blog-stats .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.blog-stats .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Featured Article */
.featured-article {
    padding: 80px 0;
    background: var(--bg-secondary);
}

.featured-card {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    transition: var(--transition);
}

.featured-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.featured-image {
    position: relative;
    overflow: hidden;
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.featured-card:hover .featured-image img {
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.featured-content {
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.featured-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.featured-meta .category {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 500;
}

.featured-meta .read-time,
.featured-meta .date {
    color: var(--text-light);
}

.featured-title {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.featured-excerpt {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.featured-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
}

.featured-link:hover {
    color: var(--primary-dark);
}

.featured-link .arrow {
    transition: var(--transition);
}

.featured-link:hover .arrow {
    transform: translateX(5px);
}

/* Blog Grid */
.blog-grid-section {
    padding: 80px 0;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.blog-card {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.blog-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

.blog-category {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.blog-content {
    padding: 1.5rem;
}

.blog-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: var(--text-light);
}

.blog-title {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.blog-excerpt {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.blog-link {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
}

.blog-link:hover {
    color: var(--primary-dark);
}

/* Newsletter Section */
.newsletter-section {
    padding: 60px 0;
    background: var(--bg-secondary);
}

.newsletter-card {
    background: white;
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 2rem;
    align-items: center;
}

.newsletter-content h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.newsletter-content p {
    color: var(--text-secondary);
    margin: 0;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
}

.newsletter-input {
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    min-width: 250px;
    transition: var(--transition);
}

.newsletter-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.newsletter-btn {
    padding: 0.75rem 1.5rem;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.newsletter-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Article Page Styles */
.article-header {
    padding: 120px 0 60px;
    background: var(--bg-secondary);
    text-align: center;
}

.article-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

.article-title {
    font-size: 3rem;
    color: var(--text-primary);
    max-width: 800px;
    margin: 0 auto 1rem;
    line-height: 1.2;
}

.article-excerpt {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.article-content {
    padding: 60px 0;
}

.article-body {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-primary);
}

.article-body h2 {
    margin-top: 3rem;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.article-body h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.article-body p {
    margin-bottom: 1.5rem;
}

.article-body img {
    width: 100%;
    border-radius: var(--border-radius);
    margin: 2rem 0;
    box-shadow: var(--shadow-md);
}

.article-body blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 2rem;
    margin: 2rem 0;
    font-style: italic;
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: 1.5rem 2rem;
    border-radius: var(--border-radius);
}

/* Recommended Articles Section */
.recommended-section {
    padding: 60px 0;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.recommended-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.recommended-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.recommended-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.recommended-image {
    height: 160px;
    overflow: hidden;
    position: relative;
}

.recommended-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.recommended-card:hover .recommended-image img {
    transform: scale(1.05);
}

.recommended-category {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.recommended-content {
    padding: 1.25rem;
}

.recommended-meta {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.8rem;
    color: var(--text-light);
}

.recommended-title {
    font-size: 1.1rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.recommended-excerpt {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.recommended-link {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.recommended-link:hover {
    color: var(--primary-dark);
}

.recommended-link::after {
    content: '→';
    transition: var(--transition);
}

.recommended-link:hover::after {
    transform: translateX(3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .blog-hero-title {
        font-size: 2.5rem;
    }

    .blog-stats {
        gap: 2rem;
    }

    .featured-card {
        grid-template-columns: 1fr;
    }

    .featured-content {
        padding: 2rem;
    }

    .blog-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .newsletter-card {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .newsletter-input {
        min-width: auto;
    }

    .article-title {
        font-size: 2rem;
    }

    .article-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .recommended-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .recommended-section {
        padding: 40px 0;
    }
}
