/* Contact Page Specific Styles */

/* Contact Hero Section */
.contact-hero {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.contact-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.contact-hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.contact-title {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.contact-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 300;
}

.contact-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Contact Content Section */
.contact-content {
    padding: 6rem 0;
    background: #f8fafc;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* Contact Form Section */
.contact-form-section {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.form-header {
    margin-bottom: 2rem;
}

.form-header h2 {
    font-size: 2rem;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #64748b;
    font-size: 1.1rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #9ca3af;
}

/* Contact Info Section */
.contact-info-section {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.contact-info h3,
.faq-section h3 {
    font-size: 1.5rem;
    color: #1a202c;
    margin-bottom: 1.5rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 1rem;
}

.method-icon {
    font-size: 1.5rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    flex-shrink: 0;
}

.method-content h4 {
    font-size: 1.1rem;
    color: #1a202c;
    margin-bottom: 0.25rem;
}

.method-content p {
    color: #4a5568;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.method-note {
    font-size: 0.9rem;
    color: #64748b;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.social-links a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #764ba2;
}

/* FAQ Section */
.faq-section {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.faq-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.faq-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.faq-item h4 {
    font-size: 1.1rem;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

.faq-item p {
    color: #64748b;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-hero {
        padding: 6rem 0 3rem;
    }
    
    .contact-title {
        font-size: 2.5rem;
    }
    
    .contact-subtitle {
        font-size: 1.2rem;
    }
    
    .contact-description {
        font-size: 1rem;
    }
    
    .contact-content {
        padding: 4rem 0;
    }
    
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1rem;
    }
    
    .contact-form-section {
        padding: 2rem;
    }
    
    .form-header h2 {
        font-size: 1.5rem;
    }
    
    .contact-method {
        padding: 1rem;
    }
    
    .method-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .faq-section {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .contact-hero-content {
        padding: 0 1rem;
    }
    
    .contact-title {
        font-size: 2rem;
    }
    
    .contact-form-section {
        padding: 1.5rem;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.75rem;
    }
}
