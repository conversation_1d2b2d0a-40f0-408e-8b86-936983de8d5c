/* Logcipher - Main Styles */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #ec4899;
    --accent-color: #06b6d4;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-dark: #111827;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-secondary: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}
.card-preview p{
    color: #e2e7f0;
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
    min-width: 120px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.btn-tool {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    width: 100%;
    margin-top: 1rem;
}

.btn-tool:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Header and Navigation */
.header {
    position: sticky;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(236, 72, 153, 0.05));
    opacity: 0;
    transition: var(--transition);
}

.header:hover .header-background {
    opacity: 1;
}

.navbar {
    padding: 1rem 0;
    position: relative;
    z-index: 1;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    font-size: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: float 3s ease-in-out infinite;
}

.logo-text h1 {
    font-size: 1.5rem;
    margin: 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-tagline {
    font-size: 0.75rem;
    color: var(--text-light);
    display: block;
    margin-top: -5px;
    font-weight: 500;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
}

.nav-link:hover {
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    transform: translateY(-2px);
}

.nav-link.active {
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.15);
    font-weight: 600;
}

.nav-icon {
    font-size: 1rem;
    opacity: 0.8;
}

.nav-text {
    font-size: 0.95rem;
}

.nav-cta {
    display: flex;
    align-items: center;
}

.btn-nav-cta {
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1.25rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

.btn-nav-cta:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition);
    border-radius: 2px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.6;
    animation: float 8s ease-in-out infinite;
}

.hero-orb-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.4), transparent);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.hero-orb-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(236, 72, 153, 0.4), transparent);
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.hero-orb-3 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(6, 182, 212, 0.4), transparent);
    bottom: 20%;
    left: 60%;
    animation-delay: 4s;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

.hero-content {
    color: white;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.badge-icon {
    font-size: 1rem;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.btn-icon {
    margin-right: 0.5rem;
}

.hero-features {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.feature-item {
    /* display: flex; */
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

.feature-icon {
    font-size: 1rem;
}

.hero-visual {
    position: relative;
    height: 500px;
}

.hero-showcase {
    position: relative;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    align-content: center;
}

.showcase-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    color: white;
    transition: var(--transition);
    animation: float 8s ease-in-out infinite;
}

.showcase-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.showcase-story {
    animation-delay: 0s;
}

.showcase-dialogue {
    animation-delay: 2s;
}

.showcase-poetry {
    animation-delay: 4s;
}

.showcase-title {
    animation-delay: 6s;
}

.card-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.card-icon {
    font-size: 1.5rem;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    color: #fff;
}

.card-preview {
    margin-bottom: 1rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.9);
    font-style: italic;
}

.card-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

/* Tools Section */
.tools-section {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

/* Tool Tabs */
.tool-tabs {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.tool-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: white;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    color: var(--text-secondary);
    min-width: 160px;
    justify-content: center;
}

.tool-tab:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.tool-tab.active {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.tab-icon {
    font-size: 1.2rem;
}

.tab-text {
    font-size: 0.95rem;
}

/* Tool Content Area */
.tool-content-area {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.tool-panel {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tool-panel.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.panel-header {
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.panel-title {
    font-size: 1.75rem;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.panel-description {
    color: var(--text-secondary);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.panel-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Panel Workspace */
.panel-workspace {
    padding: 0;
}

.workspace-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 500px;
}

.workspace-input {
    padding: 2rem;
    border-right: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.workspace-input label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.workspace-input textarea {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    resize: vertical;
    transition: var(--transition);
    background: white;
    margin-bottom: 1.5rem;
}

.workspace-input textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.input-group label {
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.input-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 0.95rem;
    background: white;
    transition: var(--transition);
}

.input-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.btn-generate {
    width: 100%;
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-md);
}

.btn-generate:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-generate:active {
    transform: translateY(0);
}

.btn-loading {
    display: none;
}

.btn-generate.loading .btn-text,
.btn-generate.loading .btn-icon {
    display: none;
}

.btn-generate.loading .btn-loading {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Workspace Output */
.workspace-output {
    padding: 2rem;
    background: white;
    display: flex;
    flex-direction: column;
}

.output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.output-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.output-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-action {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.btn-action:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.action-icon {
    font-size: 0.875rem;
}

.result-container {
    flex: 1;
    min-height: 300px;
    display: flex;
    flex-direction: column;
}

.result {
    flex: 1;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    white-space: pre-wrap;
    line-height: 1.7;
    font-family: 'Georgia', serif;
    color: var(--text-primary);
    font-size: 1rem;
    overflow-y: auto;
    max-height: 400px;
}

.result:empty {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-style: italic;
    font-family: inherit;
}

.result:empty::before {
    content: "Generated content will appear here...";
}

/* Showcase Section */
.showcase-section {
    padding: 6rem 0;
    background: white;
}

.showcase-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.showcase-item {
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.showcase-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.showcase-category {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.showcase-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
    margin-top: 0.5rem;
}

.showcase-excerpt {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.showcase-meta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.showcase-genre,
.showcase-length {
    background: white;
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    border: 1px solid var(--border-color);
}

.showcase-cta {
    text-align: center;
    padding: 3rem 0 1rem;
    border-top: 1px solid var(--border-color);
}

.showcase-cta p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.tool-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 0;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.tool-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 2rem 2rem 1rem;
}

.tool-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
}

.tool-info {
    flex: 1;
}

.tool-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.tool-description {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
    margin: 0;
}

.btn-tool-toggle {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.btn-tool-toggle:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-tool-toggle.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.toggle-icon {
    transition: var(--transition);
    font-size: 0.75rem;
}

.btn-tool-toggle.active .toggle-icon {
    transform: rotate(180deg);
}

.tool-genres {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0 2rem 1rem;
}

.genre-tag {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.genre-tag:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Tool Workspace Styles */
.tool-workspace {
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 1000px;
    }
}

.workspace-input {
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.workspace-input label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.workspace-input textarea {
    width: 100%;
    min-height: 100px;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 0.95rem;
    resize: vertical;
    transition: var(--transition);
    background: white;
}

.workspace-input textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.option-group label {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    color: var(--text-secondary);
}

.option-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 0.9rem;
    background: white;
    transition: var(--transition);
}

.option-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.workspace-output {
    padding: 2rem;
    min-height: 100px;
}

.workspace-output .result {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    white-space: pre-wrap;
    line-height: 1.6;
    font-family: 'Georgia', serif;
    color: var(--text-primary);
    max-height: 400px;
    overflow-y: auto;
}

.workspace-output .result:empty {
    display: none;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.close {
    color: var(--text-light);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
}

.close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 2rem;
}

.input-section {
    margin-bottom: 2rem;
}

.input-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

#promptInput {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    resize: vertical;
    transition: var(--transition);
}

#promptInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-options {
    margin: 1rem 0;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.option-group {
    flex: 1;
    min-width: 200px;
}

.option-group label {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.option-group select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
}

/* Loading Animation */
.loading {
    text-align: center;
    padding: 2rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.result {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 1rem;
    border: 1px solid var(--border-color);
    white-space: pre-wrap;
    line-height: 1.6;
    font-family: 'Georgia', serif;
}

/* Features Section */
.features-section {
    padding: 6rem 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem 1rem;
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
}

/* Contact Section Styles */
.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.contact-method:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.contact-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.contact-details h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.contact-details p {
    margin: 0 0 0.5rem 0;
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
}

.contact-details p:last-child {
    margin-bottom: 0;
}

.email-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.email-link:hover {
    text-decoration: underline;
}

.contact-description {
    font-size: 0.875rem !important;
    color: var(--text-secondary) !important;
}

/* Footer */
.footer {
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    padding: 4rem 0 2rem;
}

.footer-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-main {
    display: grid;
    grid-template-columns: 1.7fr 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.footer-tools {
    display: flex;
    justify-content: center;
    margin: 0 auto;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.brand-info {
    max-width: 500px;
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.brand-highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-description {
    color: #64748b;
    line-height: 1.6;
    font-size: 0.95rem;
}

.social-icons {
    display: flex;
    gap: 0.75rem;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #e2e8f0;
    border-radius: 50%;
    color: #64748b;
    text-decoration: none;
    transition: all 0.2s ease;
}

.social-icon:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.footer-links {
    display: flex;
    justify-content: flex-end;
}

.links-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.links-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.links-section li {
    margin-bottom: 0.5rem;
}

.links-section a {
    color: #64748b;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.links-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.footer-bottom p {
    color: #94a3b8;
    font-size: 0.875rem;
    margin: 0;
}


/* Usage Section */
.usage-section {
    padding: 6rem 0;
    background: var(--bg-secondary);
}
.usage-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}
.usage-step {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
}
.usage-icon { font-size: 1.75rem; margin-bottom: .5rem; }
.usage-step h3 { font-size: 1.25rem; margin-bottom: .5rem; }

.usage-tips { display: grid; grid-template-columns: repeat(auto-fit, minmax(260px,1fr)); gap: 1rem; }
.tip { display: flex; align-items: flex-start; gap: .75rem; background: white; border: 1px solid var(--border-color); border-radius: var(--border-radius); padding: 1rem; }
.tip-icon { font-size: 1.25rem; }
.tip h4 { margin: 0 0 .25rem; }

/* Testimonials */
.testimonials-section { padding: 6rem 0; background: white; }
.testimonials-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(260px, 1fr)); gap: 1.5rem; }
.testimonial-card { background: var(--bg-secondary); border: 1px solid var(--border-color); border-radius: var(--border-radius-lg); padding: 1.5rem; display: flex; flex-direction: column; gap: .5rem; }
.avatar { font-size: 2rem; }
.stars { color: #f59e0b; letter-spacing: 2px; }
.quote { color: var(--text-primary); font-style: italic; }
.author { color: var(--text-secondary); font-weight: 600; font-size: .95rem; }
