// Blog specific JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Reading Progress Bar
    initReadingProgress();
    
    // Table of Contents
    generateTableOfContents();
    
    // Smooth scrolling for TOC links
    initSmoothScrolling();
    
    // Background animations
    createFloatingShapes();
});

// Reading Progress Bar
function initReadingProgress() {
    const progressBar = document.querySelector('.reading-progress-bar');
    if (!progressBar) return;
    
    function updateProgress() {
        const article = document.querySelector('.article-body');
        if (!article) return;
        
        const articleTop = article.offsetTop;
        const articleHeight = article.offsetHeight;
        const windowHeight = window.innerHeight;
        const scrollTop = window.pageYOffset;
        
        const articleBottom = articleTop + articleHeight - windowHeight;
        const progress = Math.max(0, Math.min(100, ((scrollTop - articleTop) / (articleBottom - articleTop)) * 100));
        
        progressBar.style.width = progress + '%';
    }
    
    window.addEventListener('scroll', updateProgress);
    updateProgress();
}

// Generate Table of Contents
function generateTableOfContents() {
    const tocContainer = document.querySelector('.toc-list');
    const headings = document.querySelectorAll('.article-body h2, .article-body h3');
    
    if (!tocContainer || headings.length === 0) return;
    
    headings.forEach((heading, index) => {
        // Create ID for heading if it doesn't exist
        if (!heading.id) {
            heading.id = 'heading-' + index;
        }
        
        const li = document.createElement('li');
        li.className = 'toc-item';
        
        const link = document.createElement('a');
        link.href = '#' + heading.id;
        link.className = 'toc-link';
        link.textContent = heading.textContent;
        
        if (heading.tagName === 'H3') {
            link.style.paddingLeft = '2rem';
            link.style.fontSize = '0.85rem';
        }
        
        li.appendChild(link);
        tocContainer.appendChild(li);
    });
    
    // Highlight current section
    highlightCurrentSection();
}

// Highlight current section in TOC
function highlightCurrentSection() {
    const tocLinks = document.querySelectorAll('.toc-link');
    const headings = document.querySelectorAll('.article-body h2, .article-body h3');
    
    if (tocLinks.length === 0 || headings.length === 0) return;
    
    function updateActiveLink() {
        let current = '';
        
        headings.forEach(heading => {
            const rect = heading.getBoundingClientRect();
            if (rect.top <= 100) {
                current = heading.id;
            }
        });
        
        tocLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    }
    
    window.addEventListener('scroll', updateActiveLink);
    updateActiveLink();
}

// Smooth scrolling for TOC links
function initSmoothScrolling() {
    document.querySelectorAll('.toc-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 100;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Create floating background shapes
function createFloatingShapes() {
    const container = document.querySelector('.blog-background-elements');
    if (!container) {
        // Create container if it doesn't exist
        const bgContainer = document.createElement('div');
        bgContainer.className = 'blog-background-elements';
        document.body.appendChild(bgContainer);
        
        // Create floating shapes
        for (let i = 0; i < 3; i++) {
            const shape = document.createElement('div');
            shape.className = 'floating-shape';
            bgContainer.appendChild(shape);
        }
    }
}

// Share functionality
function shareArticle(platform) {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    
    let shareUrl = '';
    
    switch(platform) {
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
            break;
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
            break;
        case 'linkedin':
            shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
            break;
    }
    
    if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
    }
}

// Copy link to clipboard
function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        // Show success message
        showNotification('Link copied to clipboard!');
    });
}

// Show notification
function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--primary-color);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        font-weight: 600;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Enhanced image loading with fade-in effect
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('.article-body img, .sidebar-recommended-image img');
    
    images.forEach(img => {
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.5s ease';
        
        img.addEventListener('load', function() {
            this.style.opacity = '1';
        });
        
        // If image is already loaded
        if (img.complete) {
            img.style.opacity = '1';
        }
    });
});

// Add reading time estimation
function estimateReadingTime() {
    const article = document.querySelector('.article-body');
    if (!article) return;
    
    const text = article.textContent || article.innerText;
    const wordsPerMinute = 200;
    const words = text.trim().split(/\s+/).length;
    const readingTime = Math.ceil(words / wordsPerMinute);
    
    // Update reading time in meta if element exists
    const readingTimeElement = document.querySelector('.reading-time');
    if (readingTimeElement) {
        readingTimeElement.textContent = `${readingTime} min read`;
    }
}

// Initialize reading time estimation
document.addEventListener('DOMContentLoaded', estimateReadingTime);
